{"name": "@p2p/types", "version": "1.0.0", "description": "Shared TypeScript types for P2P application", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "devDependencies": {"typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}}