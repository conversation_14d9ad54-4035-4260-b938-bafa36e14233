{"version": 3, "file": "P2PServiceContract.d.ts", "sourceRoot": "", "sources": ["P2PServiceContract.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EACV,OAAO,EACP,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,eAAe,EAChB,MAAM,aAAa,CAAC;AAGrB,UAAU,QAAQ;IAChB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,UAAU,qBAAqB;CAE9B;AAED;;;GAGG;AACH,MAAM,WAAW,WAAW;IAE1B,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAG5D,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACxC,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAG3B,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC;IACxE,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,EAAE,CAAC;IAG7C,oBAAoB,CAClB,IAAI,EAAE,QAAQ,EACd,UAAU,EAAE,MAAM,GACjB,OAAO,CAAC,YAAY,CAAC,CAAC;IACzB,kBAAkB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACtD,kBAAkB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAGtD,mBAAmB,IAAI,OAAO,CAAC;IAC/B,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAG5B,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;IAC3D,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;CAC7D;AAED;;;GAGG;AACH,MAAM,WAAW,qBAAqB;IACpC,gBAAgB,CACd,MAAM,EAAE,MAAM,EACd,QAAQ,CAAC,EAAE,QAAQ,GAClB,OAAO,CAAC,qBAAqB,CAAC,CAAC;IAClC,eAAe,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IACtC,QAAQ,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,OAAO,CAAC;IAChD,kBAAkB,CAAC,MAAM,EAAE,MAAM,GAAG,eAAe,GAAG,SAAS,CAAC;IAGhE,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,IAAI,CAAC;IACrC,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;IAC3D,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;CAC7D;AAED;;;GAGG;AACH,MAAM,WAAW,eAAe;IAC9B,WAAW,CACT,MAAM,EAAE,MAAM,EACd,OAAO,EAAE,MAAM,EACf,OAAO,CAAC,EAAE,MAAM,EAChB,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,GACvD,OAAO,CAAC;IACX,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,IAAI,CAAC;CACzD;AAED;;;GAGG;AACH,MAAM,WAAW,oBAAoB;IAEnC,QAAQ,CACN,IAAI,EAAE,QAAQ,EACd,UAAU,EAAE,MAAM,EAClB,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,GACtD,OAAO,CAAC,YAAY,CAAC,CAAC;IACzB,kBAAkB,CAChB,UAAU,EAAE,MAAM,EAClB,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,GACvD,OAAO,CAAC,IAAI,CAAC,CAAC;IACjB,kBAAkB,CAChB,UAAU,EAAE,MAAM,EAClB,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,GACvD,OAAO,CAAC,IAAI,CAAC,CAAC;IAGjB,gBAAgB,CACd,IAAI,EAAE,QAAQ,EACd,UAAU,EAAE,MAAM,EAClB,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,GACtD,YAAY,CAAC;IAChB,cAAc,CACZ,UAAU,EAAE,MAAM,EAClB,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,GACtD,IAAI,CAAC;IACR,cAAc,CACZ,UAAU,EAAE,MAAM,EAClB,YAAY,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,KAAK,OAAO,GACtD,IAAI,CAAC;CACT;AAED;;;GAGG;AACH,MAAM,WAAW,gBAAgB;IAC/B,OAAO,CAAC,YAAY,CAAC,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9C,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACxC,SAAS,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAG3B,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;IAC3D,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,GAAG,IAAI,CAAC;CAC7D;AAED;;;GAGG;AACH,MAAM,WAAW,UAAW,SAAQ,SAAS;IAC3C,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,YAAY,EAAE,MAAM,CAAC;CACtB;AAED;;;GAGG;AACH,MAAM,MAAM,gBAAgB,GAAG;IAC7B,gBAAgB,EAAE;QAAE,IAAI,EAAE,QAAQ,CAAA;KAAE,CAAC;IACrC,mBAAmB,EAAE;QAAE,IAAI,EAAE,QAAQ,CAAA;KAAE,CAAC;IACxC,kBAAkB,EAAE;QAAE,OAAO,EAAE,OAAO,CAAA;KAAE,CAAC;IACzC,cAAc,EAAE;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,OAAO,EAAE,OAAO,CAAA;KAAE,CAAC;IACrD,uBAAuB,EAAE;QAAE,QAAQ,EAAE,YAAY,CAAA;KAAE,CAAC;IACpD,wBAAwB,EAAE;QAAE,UAAU,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC;IACnE,wBAAwB,EAAE;QAAE,UAAU,EAAE,MAAM,CAAA;KAAE,CAAC;IACjD,qBAAqB,EAAE;QAAE,UAAU,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,MAAM,CAAA;KAAE,CAAC;IAC7D,0BAA0B,EAAE;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,KAAK,EAAE,eAAe,CAAA;KAAE,CAAC;IACvE,mBAAmB,EAAE;QAAE,MAAM,EAAE,MAAM,CAAA;KAAE,CAAC;IACxC,eAAe,EAAE;QAAE,MAAM,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAA;KAAE,CAAC;CACnD,CAAC;AAEF;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,IAAI,UAAU,CAOlE;AAED,wBAAgB,cAAc,CAAC,OAAO,EAAE,GAAG,GAAG,OAAO,IAAI,OAAO,CAQ/D"}