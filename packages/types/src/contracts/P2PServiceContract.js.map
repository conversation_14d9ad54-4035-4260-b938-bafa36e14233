{"version": 3, "file": "P2PServiceContract.js", "sourceRoot": "", "sources": ["P2PServiceContract.ts"], "names": [], "mappings": "AAAA,iCAAiC;AACjC,2DAA2D;AAqK3D;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,MAAW;IAC1C,OAAO,CACL,MAAM;QACN,OAAO,MAAM,KAAK,QAAQ;QAC1B,CAAC,OAAO,MAAM,CAAC,YAAY,KAAK,QAAQ;YACtC,OAAO,MAAM,CAAC,eAAe,KAAK,QAAQ,CAAC,CAC9C,CAAC;AACJ,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,OAAY;IACzC,OAAO,CACL,OAAO;QACP,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ;QAC9B,OAAO,OAAO,CAAC,QAAQ,KAAK,QAAQ;QACpC,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ;QACnC,OAAO,CAAC,SAAS,YAAY,IAAI,CAClC,CAAC;AACJ,CAAC"}