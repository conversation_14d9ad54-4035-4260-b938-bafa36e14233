// P2P Service Interface Contract
// This defines the expected API that the frontend requires
/**
 * Type Guards for Runtime Validation
 */
export function isValidP2PConfig(config) {
    return (config &&
        typeof config === 'object' &&
        (typeof config.signalingUrl === 'string' ||
            typeof config.signalingServer === 'string'));
}
export function isValidMessage(message) {
    return (message &&
        typeof message.id === 'string' &&
        typeof message.senderId === 'string' &&
        typeof message.content === 'string' &&
        message.timestamp instanceof Date);
}
//# sourceMappingURL=P2PServiceContract.js.map