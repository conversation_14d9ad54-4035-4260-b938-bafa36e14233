import type { Message, P2PEvent, EventHandler } from '@p2p/types';
export declare class MessageHandler {
    private eventBus;
    private messageQueue;
    private deliveryTracker;
    private maxRetries;
    private retryDelay;
    constructor();
    on<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    off<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    sendMessage(peerId: string, content: string, sendFunction: (peerId: string, data: string) => boolean, replyTo?: string): Message;
    handleReceivedMessage(peerId: string, data: string): void;
    getMessageHistory(peerId: string): Message[];
    getAllMessages(): Message[];
    markMessageAsRead(messageId: string): void;
    clearMessageHistory(peerId: string): void;
    clearAllMessageHistory(): void;
    private attemptMessageDelivery;
    private sendDeliveryConfirmation;
    private handleDeliveryConfirmation;
    private startDeliveryRetryLoop;
}
//# sourceMappingURL=MessageHandler.d.ts.map