import { EventBus } from '../utils/EventBus.js';
import { generateId } from '../utils/index.js';
export class MessageHandler {
    eventBus = new EventBus();
    messageQueue = {};
    deliveryTracker = {};
    maxRetries = 3;
    retryDelay = 1000; // 1 second
    constructor() {
        this.startDeliveryRetryLoop();
    }
    // Event handling
    on(eventType, handler) {
        this.eventBus.on(eventType, handler);
    }
    off(eventType, handler) {
        this.eventBus.off(eventType, handler);
    }
    // Send a text message
    sendMessage(peerId, content, sendFunction, replyTo) {
        const message = {
            id: generateId(),
            senderId: 'self', // This will be set by the calling code
            content,
            timestamp: new Date(),
            type: 'text',
            status: 'sending',
            replyTo,
        };
        // Add to queue
        if (!this.messageQueue[peerId]) {
            this.messageQueue[peerId] = [];
        }
        this.messageQueue[peerId].push(message);
        // Try to send immediately
        this.attemptMessageDelivery(peerId, message, sendFunction);
        return message;
    }
    // Handle received message
    handleReceivedMessage(peerId, data) {
        try {
            const messageData = JSON.parse(data);
            if (messageData.type === 'message') {
                const message = {
                    id: messageData.id,
                    senderId: peerId,
                    content: messageData.content,
                    timestamp: new Date(messageData.timestamp),
                    type: messageData.messageType || 'text',
                    status: 'delivered',
                    replyTo: messageData.replyTo,
                };
                // Send delivery confirmation
                this.sendDeliveryConfirmation(peerId, message.id);
                // Emit message received event
                this.eventBus.emit({
                    type: 'message-received',
                    message,
                });
            }
            else if (messageData.type === 'delivery-confirmation') {
                this.handleDeliveryConfirmation(messageData.messageId);
            }
        }
        catch (error) {
            console.error('Failed to parse received message:', error);
        }
    }
    // Get message history for a peer
    getMessageHistory(peerId) {
        return this.messageQueue[peerId] || [];
    }
    // Get all messages
    getAllMessages() {
        const allMessages = [];
        Object.values(this.messageQueue).forEach(messages => {
            allMessages.push(...messages);
        });
        return allMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    }
    // Mark message as read
    markMessageAsRead(messageId) {
        for (const messages of Object.values(this.messageQueue)) {
            const message = messages.find(m => m.id === messageId);
            if (message && message.status === 'delivered') {
                // In a full implementation, this would update the message status
                console.log(`Message ${messageId} marked as read`);
                break;
            }
        }
    }
    // Clear message history for a peer
    clearMessageHistory(peerId) {
        delete this.messageQueue[peerId];
    }
    // Clear all message history
    clearAllMessageHistory() {
        this.messageQueue = {};
        this.deliveryTracker = {};
    }
    attemptMessageDelivery(peerId, message, sendFunction) {
        const messageData = {
            type: 'message',
            id: message.id,
            content: message.content,
            timestamp: message.timestamp.toISOString(),
            messageType: message.type,
            replyTo: message.replyTo,
        };
        const success = sendFunction(peerId, JSON.stringify(messageData));
        if (success) {
            message.status = 'sent';
            // Track for delivery confirmation
            this.deliveryTracker[message.id] = {
                message,
                attempts: 1,
                lastAttempt: new Date(),
                delivered: false,
            };
        }
        else {
            message.status = 'failed';
            // Add to retry queue
            this.deliveryTracker[message.id] = {
                message,
                attempts: 1,
                lastAttempt: new Date(),
                delivered: false,
            };
        }
    }
    sendDeliveryConfirmation(peerId, messageId) {
        const confirmationData = {
            type: 'delivery-confirmation',
            messageId,
            timestamp: new Date().toISOString(),
        };
        // This would use the same send function, but we don't have access to it here
        // In a real implementation, this would be handled by the connection manager
        console.log(`Sending delivery confirmation for message ${messageId} to peer ${peerId}`);
    }
    handleDeliveryConfirmation(messageId) {
        const tracker = this.deliveryTracker[messageId];
        if (tracker) {
            tracker.delivered = true;
            tracker.message.status = 'delivered';
            // Remove from tracking
            delete this.deliveryTracker[messageId];
            console.log(`Message ${messageId} delivered successfully`);
        }
    }
    startDeliveryRetryLoop() {
        setInterval(() => {
            const now = new Date();
            for (const [messageId, tracker] of Object.entries(this.deliveryTracker)) {
                if (tracker.delivered)
                    continue;
                const timeSinceLastAttempt = now.getTime() - tracker.lastAttempt.getTime();
                const shouldRetry = timeSinceLastAttempt >= this.retryDelay &&
                    tracker.attempts < this.maxRetries;
                if (shouldRetry) {
                    tracker.attempts++;
                    tracker.lastAttempt = now;
                    // In a real implementation, this would retry sending the message
                    console.log(`Retrying message ${messageId} (attempt ${tracker.attempts})`);
                    if (tracker.attempts >= this.maxRetries) {
                        tracker.message.status = 'failed';
                        console.log(`Message ${messageId} failed after ${this.maxRetries} attempts`);
                    }
                }
            }
        }, this.retryDelay);
    }
}
//# sourceMappingURL=MessageHandler.js.map