{"version": 3, "file": "MessageHandler.js", "sourceRoot": "", "sources": ["MessageHandler.ts"], "names": [], "mappings": "AAKA,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAe/C,MAAM,OAAO,cAAc;IACjB,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC1B,YAAY,GAAiB,EAAE,CAAC;IAChC,eAAe,GAAoB,EAAE,CAAC;IACtC,UAAU,GAAG,CAAC,CAAC;IACf,UAAU,GAAG,IAAI,CAAC,CAAC,WAAW;IAEtC;QACE,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED,iBAAiB;IACjB,EAAE,CAAqB,SAAoB,EAAE,OAAwB;QACnE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,GAAG,CAAqB,SAAoB,EAAE,OAAwB;QACpE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,sBAAsB;IACtB,WAAW,CACT,MAAc,EACd,OAAe,EACf,YAAuD,EACvD,OAAgB;QAEhB,MAAM,OAAO,GAAY;YACvB,EAAE,EAAE,UAAU,EAAE;YAChB,QAAQ,EAAE,MAAM,EAAE,uCAAuC;YACzD,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,IAAI,EAAE,MAAM;YACZ,MAAM,EAAE,SAAS;YACjB,OAAO;SACR,CAAC;QAEF,eAAe;QACf,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAExC,0BAA0B;QAC1B,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAE3D,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,0BAA0B;IAC1B,qBAAqB,CAAC,MAAc,EAAE,IAAY;QAChD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAErC,IAAI,WAAW,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBACnC,MAAM,OAAO,GAAY;oBACvB,EAAE,EAAE,WAAW,CAAC,EAAE;oBAClB,QAAQ,EAAE,MAAM;oBAChB,OAAO,EAAE,WAAW,CAAC,OAAO;oBAC5B,SAAS,EAAE,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;oBAC1C,IAAI,EAAE,WAAW,CAAC,WAAW,IAAI,MAAM;oBACvC,MAAM,EAAE,WAAW;oBACnB,OAAO,EAAE,WAAW,CAAC,OAAO;iBAC7B,CAAC;gBAEF,6BAA6B;gBAC7B,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;gBAElD,8BAA8B;gBAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,kBAAkB;oBACxB,OAAO;iBACR,CAAC,CAAC;YAEL,CAAC;iBAAM,IAAI,WAAW,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;gBACxD,IAAI,CAAC,0BAA0B,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED,iCAAiC;IACjC,iBAAiB,CAAC,MAAc;QAC9B,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;IACzC,CAAC;IAED,mBAAmB;IACnB,cAAc;QACZ,MAAM,WAAW,GAAc,EAAE,CAAC;QAClC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAClD,WAAW,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;QACH,OAAO,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;IACnF,CAAC;IAED,uBAAuB;IACvB,iBAAiB,CAAC,SAAiB;QACjC,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;YACxD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,SAAS,CAAC,CAAC;YACvD,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gBAC9C,iEAAiE;gBACjE,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,iBAAiB,CAAC,CAAC;gBACnD,MAAM;YACR,CAAC;QACH,CAAC;IACH,CAAC;IAED,mCAAmC;IACnC,mBAAmB,CAAC,MAAc;QAChC,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,4BAA4B;IAC5B,sBAAsB;QACpB,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;IAC5B,CAAC;IAEO,sBAAsB,CAC5B,MAAc,EACd,OAAgB,EAChB,YAAuD;QAEvD,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,SAAS;YACf,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE;YAC1C,WAAW,EAAE,OAAO,CAAC,IAAI;YACzB,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAC;QAEF,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;QAElE,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;YAExB,kCAAkC;YAClC,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG;gBACjC,OAAO;gBACP,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,SAAS,EAAE,KAAK;aACjB,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;YAE1B,qBAAqB;YACrB,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG;gBACjC,OAAO;gBACP,QAAQ,EAAE,CAAC;gBACX,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,SAAS,EAAE,KAAK;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,MAAc,EAAE,SAAiB;QAChE,MAAM,gBAAgB,GAAG;YACvB,IAAI,EAAE,uBAAuB;YAC7B,SAAS;YACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,6EAA6E;QAC7E,4EAA4E;QAC5E,OAAO,CAAC,GAAG,CAAC,6CAA6C,SAAS,YAAY,MAAM,EAAE,CAAC,CAAC;IAC1F,CAAC;IAEO,0BAA0B,CAAC,SAAiB;QAClD,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAChD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;YACzB,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;YAErC,uBAAuB;YACvB,OAAO,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEvC,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,yBAAyB,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,sBAAsB;QAC5B,WAAW,CAAC,GAAG,EAAE;YACf,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvB,KAAK,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC;gBACxE,IAAI,OAAO,CAAC,SAAS;oBAAE,SAAS;gBAEhC,MAAM,oBAAoB,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;gBAC3E,MAAM,WAAW,GAAG,oBAAoB,IAAI,IAAI,CAAC,UAAU;oBACxC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC;gBAEtD,IAAI,WAAW,EAAE,CAAC;oBAChB,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACnB,OAAO,CAAC,WAAW,GAAG,GAAG,CAAC;oBAE1B,iEAAiE;oBACjE,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,aAAa,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;oBAE3E,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;wBACxC,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,QAAQ,CAAC;wBAClC,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,iBAAiB,IAAI,CAAC,UAAU,WAAW,CAAC,CAAC;oBAC/E,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;IACtB,CAAC;CACF"}