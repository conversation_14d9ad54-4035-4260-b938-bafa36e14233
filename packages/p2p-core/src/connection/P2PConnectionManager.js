import { EventBus } from '../utils/EventBus.js';
export class P2PConnectionManager {
    connections = new Map();
    dataChannels = new Map();
    connectionStates = new Map();
    eventBus = new EventBus();
    config;
    constructor(config) {
        this.config = config;
    }
    // Event handling
    on(eventType, handler) {
        this.eventBus.on(eventType, handler);
    }
    off(eventType, handler) {
        this.eventBus.off(eventType, handler);
    }
    // Create a new peer connection
    async createConnection(peerId, peerInfo) {
        if (this.connections.has(peerId)) {
            throw new Error(`Connection to peer ${peerId} already exists`);
        }
        const connection = new RTCPeerConnection({
            iceServers: this.config.iceServers,
            iceCandidatePoolSize: 3,
            bundlePolicy: 'max-bundle',
            rtcpMuxPolicy: 'require',
        });
        // Set up connection event handlers
        this.setupConnectionHandlers(connection, peerId, peerInfo);
        // Store connection
        this.connections.set(peerId, connection);
        // Initialize connection state
        const connectionState = {
            status: 'connecting',
            peer: peerInfo,
            connection,
            reconnectAttempts: 0,
        };
        this.connectionStates.set(peerId, connectionState);
        // Emit connection state change
        this.eventBus.emit({
            type: 'connection-state-changed',
            state: connectionState,
        });
        return connection;
    }
    // Create data channel for a peer
    createDataChannel(peerId, label = 'messages') {
        const connection = this.connections.get(peerId);
        if (!connection) {
            throw new Error(`No connection found for peer ${peerId}`);
        }
        const dataChannel = connection.createDataChannel(label, {
            ordered: true,
            maxRetransmits: 3,
        });
        this.setupDataChannelHandlers(dataChannel, peerId);
        this.dataChannels.set(peerId, dataChannel);
        return dataChannel;
    }
    // Send data to a peer
    sendData(peerId, data) {
        const dataChannel = this.dataChannels.get(peerId);
        if (!dataChannel || dataChannel.readyState !== 'open') {
            console.warn(`Data channel not ready for peer ${peerId}`);
            return false;
        }
        try {
            dataChannel.send(data);
            return true;
        }
        catch (error) {
            console.error(`Failed to send data to peer ${peerId}:`, error);
            return false;
        }
    }
    // Get connection state for a peer
    getConnectionState(peerId) {
        return this.connectionStates.get(peerId);
    }
    // Get all connection states
    getAllConnectionStates() {
        return Array.from(this.connectionStates.values());
    }
    // Close connection to a peer
    closeConnection(peerId) {
        const connection = this.connections.get(peerId);
        const dataChannel = this.dataChannels.get(peerId);
        const state = this.connectionStates.get(peerId);
        if (dataChannel) {
            dataChannel.close();
            this.dataChannels.delete(peerId);
        }
        if (connection) {
            connection.close();
            this.connections.delete(peerId);
        }
        if (state) {
            state.status = 'disconnected';
            this.connectionStates.delete(peerId);
            this.eventBus.emit({
                type: 'peer-disconnected',
                peer: state.peer,
            });
        }
    }
    // Close all connections
    closeAllConnections() {
        for (const peerId of this.connections.keys()) {
            this.closeConnection(peerId);
        }
    }
    setupConnectionHandlers(connection, peerId, peerInfo) {
        connection.onicecandidate = event => {
            if (event.candidate) {
                // ICE candidate will be handled by signaling client
                console.log(`ICE candidate for peer ${peerId}:`, event.candidate);
            }
        };
        connection.onconnectionstatechange = () => {
            const state = this.connectionStates.get(peerId);
            if (!state)
                return;
            switch (connection.connectionState) {
                case 'connected':
                    state.status = 'connected';
                    state.lastConnected = new Date();
                    state.reconnectAttempts = 0;
                    this.eventBus.emit({
                        type: 'peer-connected',
                        peer: peerInfo,
                    });
                    break;
                case 'disconnected':
                case 'failed':
                    state.status = 'failed';
                    this.handleConnectionFailure(peerId);
                    break;
                case 'connecting':
                    state.status = 'connecting';
                    break;
            }
            this.eventBus.emit({
                type: 'connection-state-changed',
                state,
            });
        };
        connection.ondatachannel = event => {
            const dataChannel = event.channel;
            this.setupDataChannelHandlers(dataChannel, peerId);
            this.dataChannels.set(peerId, dataChannel);
        };
        connection.onicegatheringstatechange = () => {
            console.log(`ICE gathering state for peer ${peerId}:`, connection.iceGatheringState);
        };
    }
    setupDataChannelHandlers(dataChannel, peerId) {
        dataChannel.onopen = () => {
            console.log(`Data channel opened for peer ${peerId}`);
            const state = this.connectionStates.get(peerId);
            if (state) {
                state.dataChannel = dataChannel;
            }
        };
        dataChannel.onclose = () => {
            console.log(`Data channel closed for peer ${peerId}`);
        };
        dataChannel.onerror = error => {
            console.error(`Data channel error for peer ${peerId}:`, error);
        };
        dataChannel.onmessage = event => {
            try {
                const message = JSON.parse(event.data);
                console.log(`Message received from peer ${peerId}:`, message);
                // Emit data received event for the message handler to process
                this.eventBus.emit({
                    type: 'data-received',
                    peerId,
                    data: event.data,
                });
            }
            catch (error) {
                console.error(`Failed to parse message from peer ${peerId}:`, error);
            }
        };
    }
    async handleConnectionFailure(peerId) {
        const state = this.connectionStates.get(peerId);
        if (!state)
            return;
        state.reconnectAttempts++;
        if (state.reconnectAttempts <= this.config.maxReconnectAttempts) {
            const delay = Math.min(this.config.reconnectDelay * Math.pow(2, state.reconnectAttempts - 1), 30000 // Max 30 seconds
            );
            console.log(`Attempting to reconnect to peer ${peerId} in ${delay}ms (attempt ${state.reconnectAttempts})`);
            setTimeout(() => {
                this.attemptReconnection(peerId);
            }, delay);
        }
        else {
            console.log(`Max reconnection attempts reached for peer ${peerId}`);
            this.closeConnection(peerId);
        }
    }
    async attemptReconnection(peerId) {
        const state = this.connectionStates.get(peerId);
        if (!state)
            return;
        try {
            // Close existing connection
            const oldConnection = this.connections.get(peerId);
            if (oldConnection) {
                oldConnection.close();
            }
            // Create new connection
            await this.createConnection(peerId, state.peer);
            console.log(`Reconnection attempt for peer ${peerId} initiated`);
        }
        catch (error) {
            console.error(`Reconnection failed for peer ${peerId}:`, error);
            this.handleConnectionFailure(peerId);
        }
    }
}
//# sourceMappingURL=P2PConnectionManager.js.map