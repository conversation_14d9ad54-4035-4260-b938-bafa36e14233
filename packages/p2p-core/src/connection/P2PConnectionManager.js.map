{"version": 3, "file": "P2PConnectionManager.js", "sourceRoot": "", "sources": ["P2PConnectionManager.ts"], "names": [], "mappings": "AAOA,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAGhD,MAAM,OAAO,oBAAoB;IACvB,WAAW,GAAG,IAAI,GAAG,EAA6B,CAAC;IACnD,YAAY,GAAG,IAAI,GAAG,EAA0B,CAAC;IACjD,gBAAgB,GAAG,IAAI,GAAG,EAA2B,CAAC;IACtD,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC1B,MAAM,CAAY;IAE1B,YAAY,MAAiB;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,iBAAiB;IACjB,EAAE,CAAqB,SAAoB,EAAE,OAAwB;QACnE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,GAAG,CACD,SAAoB,EACpB,OAAwB;QAExB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,+BAA+B;IAC/B,KAAK,CAAC,gBAAgB,CACpB,MAAc,EACd,QAAkB;QAElB,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CAAC,sBAAsB,MAAM,iBAAiB,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC;YACvC,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU;YAClC,oBAAoB,EAAE,CAAC;YACvB,YAAY,EAAE,YAAY;YAC1B,aAAa,EAAE,SAAS;SACzB,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAE3D,mBAAmB;QACnB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;QAEzC,8BAA8B;QAC9B,MAAM,eAAe,GAAoB;YACvC,MAAM,EAAE,YAAY;YACpB,IAAI,EAAE,QAAQ;YACd,UAAU;YACV,iBAAiB,EAAE,CAAC;SACrB,CAAC;QACF,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC;QAEnD,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,0BAA0B;YAChC,KAAK,EAAE,eAAe;SACvB,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,iCAAiC;IACjC,iBAAiB,CAAC,MAAc,EAAE,KAAK,GAAG,UAAU;QAClD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,gCAAgC,MAAM,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,MAAM,WAAW,GAAG,UAAU,CAAC,iBAAiB,CAAC,KAAK,EAAE;YACtD,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,CAAC;SAClB,CAAC,CAAC;QAEH,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAE3C,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,sBAAsB;IACtB,QAAQ,CAAC,MAAc,EAAE,IAA0B;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;YACtD,OAAO,CAAC,IAAI,CAAC,mCAAmC,MAAM,EAAE,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAC/D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,kCAAkC;IAClC,kBAAkB,CAAC,MAAc;QAC/B,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAC3C,CAAC;IAED,4BAA4B;IAC5B,sBAAsB;QACpB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,6BAA6B;IAC7B,eAAe,CAAC,MAAc;QAC5B,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAChD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEhD,IAAI,WAAW,EAAE,CAAC;YAChB,WAAW,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC;QAED,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC;YAC9B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAErC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,mBAAmB;gBACzB,IAAI,EAAE,KAAK,CAAC,IAAI;aACjB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,wBAAwB;IACxB,mBAAmB;QACjB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,uBAAuB,CAC7B,UAA6B,EAC7B,MAAc,EACd,QAAkB;QAElB,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE;YAClC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpB,oDAAoD;gBACpD,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,GAAG,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;YACpE,CAAC;QACH,CAAC,CAAC;QAEF,UAAU,CAAC,uBAAuB,GAAG,GAAG,EAAE;YACxC,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,KAAK;gBAAE,OAAO;YAEnB,QAAQ,UAAU,CAAC,eAAe,EAAE,CAAC;gBACnC,KAAK,WAAW;oBACd,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC;oBAC3B,KAAK,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;oBACjC,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC;oBAC5B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;wBACjB,IAAI,EAAE,gBAAgB;wBACtB,IAAI,EAAE,QAAQ;qBACf,CAAC,CAAC;oBACH,MAAM;gBACR,KAAK,cAAc,CAAC;gBACpB,KAAK,QAAQ;oBACX,KAAK,CAAC,MAAM,GAAG,QAAQ,CAAC;oBACxB,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;oBACrC,MAAM;gBACR,KAAK,YAAY;oBACf,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC;oBAC5B,MAAM;YACV,CAAC;YAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,0BAA0B;gBAChC,KAAK;aACN,CAAC,CAAC;QACL,CAAC,CAAC;QAEF,UAAU,CAAC,aAAa,GAAG,KAAK,CAAC,EAAE;YACjC,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC;YAClC,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;QAC7C,CAAC,CAAC;QAEF,UAAU,CAAC,yBAAyB,GAAG,GAAG,EAAE;YAC1C,OAAO,CAAC,GAAG,CACT,gCAAgC,MAAM,GAAG,EACzC,UAAU,CAAC,iBAAiB,CAC7B,CAAC;QACJ,CAAC,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAC9B,WAA2B,EAC3B,MAAc;QAEd,WAAW,CAAC,MAAM,GAAG,GAAG,EAAE;YACxB,OAAO,CAAC,GAAG,CAAC,gCAAgC,MAAM,EAAE,CAAC,CAAC;YACtD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;YAClC,CAAC;QACH,CAAC,CAAC;QAEF,WAAW,CAAC,OAAO,GAAG,GAAG,EAAE;YACzB,OAAO,CAAC,GAAG,CAAC,gCAAgC,MAAM,EAAE,CAAC,CAAC;QACxD,CAAC,CAAC;QAEF,WAAW,CAAC,OAAO,GAAG,KAAK,CAAC,EAAE;YAC5B,OAAO,CAAC,KAAK,CAAC,+BAA+B,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QACjE,CAAC,CAAC;QAEF,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE;YAC9B,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACvC,OAAO,CAAC,GAAG,CAAC,8BAA8B,MAAM,GAAG,EAAE,OAAO,CAAC,CAAC;gBAE9D,8DAA8D;gBAC9D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,eAAe;oBACrB,MAAM;oBACN,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,CAAC;QACH,CAAC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAClD,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAE1B,IAAI,KAAK,CAAC,iBAAiB,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAChE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CACpB,IAAI,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,iBAAiB,GAAG,CAAC,CAAC,EACrE,KAAK,CAAC,iBAAiB;aACxB,CAAC;YAEF,OAAO,CAAC,GAAG,CACT,mCAAmC,MAAM,OAAO,KAAK,eAAe,KAAK,CAAC,iBAAiB,GAAG,CAC/F,CAAC;YAEF,UAAU,CAAC,GAAG,EAAE;gBACd,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC,EAAE,KAAK,CAAC,CAAC;QACZ,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,8CAA8C,MAAM,EAAE,CAAC,CAAC;YACpE,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,MAAc;QAC9C,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAChD,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAI,CAAC;YACH,4BAA4B;YAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,aAAa,EAAE,CAAC;gBAClB,aAAa,CAAC,KAAK,EAAE,CAAC;YACxB,CAAC;YAED,wBAAwB;YACxB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAChD,OAAO,CAAC,GAAG,CAAC,iCAAiC,MAAM,YAAY,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAChE,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;CACF"}