import type { ConnectionState, PeerInfo, P2PConfig, P2PEvent, EventHandler } from '@p2p/types';
export declare class P2PConnectionManager {
    private connections;
    private dataChannels;
    private connectionStates;
    private eventBus;
    private config;
    constructor(config: P2PConfig);
    on<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    off<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    createConnection(peerId: string, peerInfo: PeerInfo): Promise<RTCPeerConnection>;
    createDataChannel(peerId: string, label?: string): RTCDataChannel;
    sendData(peerId: string, data: string | ArrayBuffer): boolean;
    getConnectionState(peerId: string): ConnectionState | undefined;
    getAllConnectionStates(): ConnectionState[];
    closeConnection(peerId: string): void;
    closeAllConnections(): void;
    private setupConnectionHandlers;
    private setupDataChannelHandlers;
    private handleConnectionFailure;
    private attemptReconnection;
}
//# sourceMappingURL=P2PConnectionManager.d.ts.map