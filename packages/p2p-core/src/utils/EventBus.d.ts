import type { EventHand<PERSON>, P2PEvent } from '@p2p/types';
export declare class EventBus {
    private listeners;
    on<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    off<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    emit<T extends P2PEvent>(event: T): void;
    removeAllListeners(eventType?: string): void;
}
//# sourceMappingURL=EventBus.d.ts.map