export { EventBus } from './EventBus.js';
// Utility functions
export const generateId = () => {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
};
export const sleep = (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
};
export const formatFileSize = (bytes) => {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};
export const isValidRoomId = (roomId) => {
    return /^[a-zA-Z0-9-_]{3,50}$/.test(roomId);
};
//# sourceMappingURL=index.js.map