import type { P2PConfig, EventHandler, P2PEvent } from '@p2p/types';
export declare class SignalingClient {
    private socket;
    private eventBus;
    private config;
    private currentRoomId;
    private isConnected;
    constructor(config: P2PConfig);
    on<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    off<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    connect(): Promise<void>;
    disconnect(): void;
    joinRoom(roomId: string): Promise<string[]>;
    leaveRoom(): void;
    sendOffer(targetPeerId: string, offer: RTCSessionDescriptionInit): void;
    sendAnswer(targetPeerId: string, answer: RTCSessionDescriptionInit): void;
    sendIceCandidate(targetPeerId: string, candidate: RTCIceCandidateInit): void;
    getCurrentRoomId(): string | null;
    getIsConnected(): boolean;
    private setupSocketHandlers;
}
//# sourceMappingURL=SignalingClient.d.ts.map