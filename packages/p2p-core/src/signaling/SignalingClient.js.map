{"version": 3, "file": "SignalingClient.js", "sourceRoot": "", "sources": ["SignalingClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,EAAE,EAAU,MAAM,kBAAkB,CAAC;AAQ9C,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAehD,MAAM,OAAO,eAAe;IAClB,MAAM,GAAkB,IAAI,CAAC;IAC7B,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC1B,MAAM,CAAY;IAClB,aAAa,GAAkB,IAAI,CAAC;IACpC,WAAW,GAAG,KAAK,CAAC;IAE5B,YAAY,MAAiB;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,iBAAiB;IACjB,EAAE,CAAqB,SAAoB,EAAE,OAAwB;QACnE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,GAAG,CACD,SAAoB,EACpB,OAAwB;QAExB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,8BAA8B;IAC9B,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;gBACzC,UAAU,EAAE,CAAC,WAAW,EAAE,SAAS,CAAC;gBACpC,OAAO,EAAE,KAAK;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAE3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE;gBAC7B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,OAAO,EAAE,CAAC;YACZ,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,KAAK,CAAC,EAAE;gBACtC,OAAO,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;gBAC/D,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;gBACzB,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,mCAAmC;IACnC,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;YACzB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;YACnB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,cAAc;IACd,KAAK,CAAC,QAAQ,CAAC,MAAc;QAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,EAAE;gBAC9B,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;YACzC,CAAC,EAAE,KAAK,CAAC,CAAC;YAEV,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,IAAqB,EAAE,EAAE;gBACzD,YAAY,CAAC,OAAO,CAAC,CAAC;gBACtB,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;gBAC5B,OAAO,CAAC,GAAG,CAAC,eAAe,MAAM,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC7D,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,qBAAqB;IACrB,SAAS;QACP,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YAC/B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC;IACH,CAAC;IAED,oBAAoB;IACpB,SAAS,CAAC,YAAoB,EAAE,KAAgC;QAC9D,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;YACxB,MAAM,EAAE,YAAY;YACpB,KAAK;SACN,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,sBAAsB,YAAY,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,qBAAqB;IACrB,UAAU,CAAC,YAAoB,EAAE,MAAiC;QAChE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;YACzB,MAAM,EAAE,YAAY;YACpB,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,uBAAuB,YAAY,EAAE,CAAC,CAAC;IACrD,CAAC;IAED,qBAAqB;IACrB,gBAAgB,CAAC,YAAoB,EAAE,SAA8B;QACnE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,EAAE;YAChC,MAAM,EAAE,YAAY;YACpB,SAAS;SACV,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,8BAA8B,YAAY,EAAE,CAAC,CAAC;IAC5D,CAAC;IAED,sBAAsB;IACtB,gBAAgB;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,qBAAqB;IACrB,cAAc;QACZ,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,EAAE,SAAS,KAAK,IAAI,CAAC;IAC7D,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,MAAM;YAAE,OAAO;QAEzB,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE;YACpC,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAC;YAC3D,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC5B,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,CAAC,IAAqB,EAAE,EAAE;YACtD,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,MAAM,kBAAkB,CAAC,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,aAAa;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;aACb,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC;QAEH,2BAA2B;QAC3B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,IAAmB,EAAE,EAAE;YAClD,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,MAAM,gBAAgB,CAAC,CAAC;YACjD,qDAAqD;QACvD,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,EAAE,CACZ,OAAO,EACP,CAAC,IAA0D,EAAE,EAAE;YAC7D,OAAO,CAAC,GAAG,CAAC,4BAA4B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACvD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,gBAAgB;gBACtB,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,KAAK,EAAE,IAAI,CAAC,KAAK;aACX,CAAC,CAAC;QACZ,CAAC,CACF,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,EAAE,CACZ,QAAQ,EACR,CAAC,IAA2D,EAAE,EAAE;YAC9D,OAAO,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACxD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,iBAAiB;gBACvB,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,MAAM,EAAE,IAAI,CAAC,MAAM;aACb,CAAC,CAAC;QACZ,CAAC,CACF,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,EAAE,CACZ,eAAe,EACf,CAAC,IAAwD,EAAE,EAAE;YAC3D,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,wBAAwB;gBAC9B,QAAQ,EAAE,IAAI,CAAC,MAAM;gBACrB,SAAS,EAAE,IAAI,CAAC,SAAS;aACnB,CAAC,CAAC;QACZ,CAAC,CACF,CAAC;QAEF,gBAAgB;QAChB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;YAC9B,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC,EAAE;YAC1C,OAAO,CAAC,GAAG,CAAC,4CAA4C,aAAa,GAAG,CAAC,CAAC;YAC1E,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAExB,gCAAgC;YAChC,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;gBACvB,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YACrD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,EAAE,KAAK,CAAC,EAAE;YACxC,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;CACF"}