import { io } from 'socket.io-client';
import { EventBus } from '../utils/EventBus.js';
export class SignalingClient {
    socket = null;
    eventBus = new EventBus();
    config;
    currentRoomId = null;
    isConnected = false;
    constructor(config) {
        this.config = config;
    }
    // Event handling
    on(eventType, handler) {
        this.eventBus.on(eventType, handler);
    }
    off(eventType, handler) {
        this.eventBus.off(eventType, handler);
    }
    // Connect to signaling server
    async connect() {
        if (this.socket?.connected) {
            return;
        }
        return new Promise((resolve, reject) => {
            this.socket = io(this.config.signalingUrl, {
                transports: ['websocket', 'polling'],
                timeout: 10000,
            });
            this.setupSocketHandlers();
            this.socket.on('connect', () => {
                console.log('Connected to signaling server');
                this.isConnected = true;
                resolve();
            });
            this.socket.on('connect_error', error => {
                console.error('Failed to connect to signaling server:', error);
                this.isConnected = false;
                reject(error);
            });
        });
    }
    // Disconnect from signaling server
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            this.currentRoomId = null;
        }
    }
    // Join a room
    async joinRoom(roomId) {
        if (!this.socket?.connected) {
            throw new Error('Not connected to signaling server');
        }
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Room join timeout'));
            }, 10000);
            this.socket.once('room-joined', (data) => {
                clearTimeout(timeout);
                this.currentRoomId = roomId;
                console.log(`Joined room ${roomId} with peers:`, data.peers);
                resolve(data.peers);
            });
            this.socket.emit('join-room', roomId);
        });
    }
    // Leave current room
    leaveRoom() {
        if (this.socket?.connected && this.currentRoomId) {
            this.socket.emit('leave-room');
            this.currentRoomId = null;
        }
    }
    // Send WebRTC offer
    sendOffer(targetPeerId, offer) {
        if (!this.socket?.connected) {
            throw new Error('Not connected to signaling server');
        }
        this.socket.emit('offer', {
            target: targetPeerId,
            offer,
        });
        console.log(`Sent offer to peer ${targetPeerId}`);
    }
    // Send WebRTC answer
    sendAnswer(targetPeerId, answer) {
        if (!this.socket?.connected) {
            throw new Error('Not connected to signaling server');
        }
        this.socket.emit('answer', {
            target: targetPeerId,
            answer,
        });
        console.log(`Sent answer to peer ${targetPeerId}`);
    }
    // Send ICE candidate
    sendIceCandidate(targetPeerId, candidate) {
        if (!this.socket?.connected) {
            throw new Error('Not connected to signaling server');
        }
        this.socket.emit('ice-candidate', {
            target: targetPeerId,
            candidate,
        });
        console.log(`Sent ICE candidate to peer ${targetPeerId}`);
    }
    // Get current room ID
    getCurrentRoomId() {
        return this.currentRoomId;
    }
    // Check if connected
    getIsConnected() {
        return this.isConnected && this.socket?.connected === true;
    }
    setupSocketHandlers() {
        if (!this.socket)
            return;
        // Handle disconnection
        this.socket.on('disconnect', reason => {
            console.log('Disconnected from signaling server:', reason);
            this.isConnected = false;
            this.currentRoomId = null;
        });
        // Handle peer joining room
        this.socket.on('peer-joined', (data) => {
            console.log(`Peer ${data.peerId} joined the room`);
            this.eventBus.emit({
                type: 'peer-joined',
                peerId: data.peerId,
            });
        });
        // Handle peer leaving room
        this.socket.on('peer-left', (data) => {
            console.log(`Peer ${data.peerId} left the room`);
            // This will be handled by the room management system
        });
        // Handle WebRTC offer
        this.socket.on('offer', (data) => {
            console.log(`Received offer from peer ${data.sender}`);
            this.eventBus.emit({
                type: 'offer-received',
                senderId: data.sender,
                offer: data.offer,
            });
        });
        // Handle WebRTC answer
        this.socket.on('answer', (data) => {
            console.log(`Received answer from peer ${data.sender}`);
            this.eventBus.emit({
                type: 'answer-received',
                senderId: data.sender,
                answer: data.answer,
            });
        });
        // Handle ICE candidate
        this.socket.on('ice-candidate', (data) => {
            console.log(`Received ICE candidate from peer ${data.sender}`);
            this.eventBus.emit({
                type: 'ice-candidate-received',
                senderId: data.sender,
                candidate: data.candidate,
            });
        });
        // Handle errors
        this.socket.on('error', error => {
            console.error('Signaling error:', error);
        });
        // Handle reconnection
        this.socket.on('reconnect', attemptNumber => {
            console.log(`Reconnected to signaling server (attempt ${attemptNumber})`);
            this.isConnected = true;
            // Rejoin room if we were in one
            if (this.currentRoomId) {
                this.socket.emit('join-room', this.currentRoomId);
            }
        });
        this.socket.on('reconnect_error', error => {
            console.error('Reconnection failed:', error);
        });
    }
}
//# sourceMappingURL=SignalingClient.js.map