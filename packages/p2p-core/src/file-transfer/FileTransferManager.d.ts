import type { FileTransfer, P2PEvent, EventHandler } from '@p2p/types';
export declare class FileTransferManager {
    private eventBus;
    private activeTransfers;
    private chunkSize;
    constructor(chunkSize?: number);
    on<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    off<T extends P2PEvent>(eventType: T['type'], handler: EventHandler<T>): void;
    initiateTransfer(file: File, receiverId: string, sendFunction: (peerId: string, data: string) => boolean): FileTransfer;
    acceptTransfer(transferId: string, sendFunction: (peerId: string, data: string) => boolean): void;
    rejectTransfer(transferId: string, sendFunction: (peerId: string, data: string) => boolean): void;
    startSending(transferId: string, file: File, sendFunction: (peerId: string, data: string) => boolean): Promise<void>;
    handleReceivedData(senderId: string, data: string | ArrayBuffer): void;
    getActiveTransfers(): FileTransfer[];
    getTransfer(transferId: string): FileTransfer | undefined;
    private createFileChunks;
    private handleFileMessage;
    private handleTransferRequest;
    private handleTransferResponse;
    private handleFileChunk;
    private calculateChecksum;
    cancelTransfer(transferId: string, sendFunction?: (peerId: string, data: string) => boolean): void;
    pauseTransfer(transferId: string): void;
    resumeTransfer(transferId: string): void;
}
//# sourceMappingURL=FileTransferManager.d.ts.map