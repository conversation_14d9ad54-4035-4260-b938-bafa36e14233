{"version": 3, "file": "FileTransferManager.js", "sourceRoot": "", "sources": ["FileTransferManager.ts"], "names": [], "mappings": "AAMA,OAAO,EAAE,QAAQ,EAAE,MAAM,sBAAsB,CAAC;AAChD,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE/C,MAAM,OAAO,mBAAmB;IACtB,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;IAC1B,eAAe,GAAG,IAAI,GAAG,EAAwB,CAAC;IAClD,SAAS,GAAG,KAAK,CAAC,CAAC,cAAc;IAEzC,YAAY,SAAkB;QAC5B,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC7B,CAAC;IACH,CAAC;IAED,iBAAiB;IACjB,EAAE,CAAqB,SAAoB,EAAE,OAAwB;QACnE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,GAAG,CACD,SAAoB,EACpB,OAAwB;QAExB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,yBAAyB;IACzB,gBAAgB,CACd,IAAU,EACV,UAAkB,EAClB,YAAuD;QAEvD,MAAM,QAAQ,GAAiB;YAC7B,EAAE,EAAE,UAAU,EAAE;YAChB,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE,MAAM;YAChB,UAAU;YACV,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAEhD,wBAAwB;QACxB,MAAM,WAAW,GAAG;YAClB,IAAI,EAAE,uBAAuB;YAC7B,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;SACpB,CAAC;QAEF,MAAM,OAAO,GAAG,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,CAAC;QACtE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,uBAAuB;YAC7B,QAAQ;SACT,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,uBAAuB;IACvB,cAAc,CACZ,UAAkB,EAClB,YAAuD;QAEvD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC;QAC7B,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAEhC,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,wBAAwB;YAC9B,UAAU;YACV,QAAQ,EAAE,IAAI;SACf,CAAC;QAEF,YAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,uBAAuB;IACvB,cAAc,CACZ,UAAkB,EAClB,YAAuD;QAEvD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC;QAE7B,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,wBAAwB;YAC9B,UAAU;YACV,QAAQ,EAAE,KAAK;SAChB,CAAC;QAEF,YAAY,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED,wCAAwC;IACxC,KAAK,CAAC,YAAY,CAChB,UAAkB,EAClB,IAAU,EACV,YAAuD;QAEvD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,UAAU;YAAE,OAAO;QAExD,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC;QACjC,QAAQ,CAAC,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAChC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QAC7D,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QAEzB,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC;QAElC,2BAA2B;QAC3B,MAAM,eAAe,GAAG;YACtB,IAAI,EAAE,eAAe;YACrB,UAAU;YACV,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,QAAQ,EAAE,IAAI,CAAC,IAAI;YACnB,WAAW;YACX,SAAS,EAAE,IAAI,CAAC,SAAS;SAC1B,CAAC;QAEF,MAAM,YAAY,GAAG,YAAY,CAC/B,QAAQ,CAAC,UAAU,EACnB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAChC,CAAC;QACF,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,+BAA+B;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,IAAI,OAAO,GAAG,KAAK,CAAC;YAEpB,OAAO,QAAQ,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;gBAChC,MAAM,YAAY,GAAG;oBACnB,IAAI,EAAE,YAAY;oBAClB,UAAU;oBACV,UAAU,EAAE,CAAC;oBACb,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,EAAE,4BAA4B;oBAC1E,QAAQ,EAAE,MAAM,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC;iBACnD,CAAC;gBAEF,OAAO,GAAG,YAAY,CACpB,QAAQ,CAAC,UAAU,EACnB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,CAC7B,CAAC;gBAEF,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,QAAQ,EAAE,CAAC;oBACX,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,sBAAsB;gBAC3F,CAAC;YACH,CAAC;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAC3B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;oBACjB,IAAI,EAAE,wBAAwB;oBAC9B,UAAU;oBACV,QAAQ,EAAE,QAAQ,CAAC,QAAQ;iBAC5B,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,UAAU,EAAE,CAAC;YACb,QAAQ,CAAC,QAAQ,GAAG,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;YAErD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjB,IAAI,EAAE,wBAAwB;gBAC9B,UAAU;gBACV,QAAQ,EAAE,QAAQ,CAAC,QAAQ;aAC5B,CAAC,CAAC;YAEH,qDAAqD;YACrD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,0BAA0B;QAC1B,MAAM,iBAAiB,GAAG;YACxB,IAAI,EAAE,eAAe;YACrB,UAAU;YACV,WAAW,EAAE,UAAU;SACxB,CAAC;QAEF,YAAY,CAAC,QAAQ,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,CAAC;QAErE,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;QAC9B,QAAQ,CAAC,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,QAAQ,CAAC,QAAQ,GAAG,GAAG,CAAC;QAExB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,wBAAwB;YAC9B,UAAU;YACV,QAAQ,EAAE,GAAG;SACd,CAAC,CAAC;IACL,CAAC;IAED,4BAA4B;IAC5B,kBAAkB,CAAC,QAAgB,EAAE,IAA0B;QAC7D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACrC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAChD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;aAAM,CAAC;YACN,2BAA2B;YAC3B,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,uBAAuB;IACvB,kBAAkB;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;IACnD,CAAC;IAED,qBAAqB;IACrB,WAAW,CAAC,UAAkB;QAC5B,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC9C,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAC5B,IAAU,EACV,UAAkB;QAElB,MAAM,MAAM,GAAgB,EAAE,CAAC;QAC/B,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC;QAE1D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACpC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAE7C,MAAM,KAAK,GAAc;gBACvB,EAAE,EAAE,UAAU,EAAE;gBAChB,UAAU;gBACV,KAAK,EAAE,CAAC;gBACR,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,WAAW,CAAC,UAAU;aAC7B,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACrB,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,iBAAiB,CAAC,QAAgB,EAAE,WAAgB;QAC1D,QAAQ,WAAW,CAAC,IAAI,EAAE,CAAC;YACzB,KAAK,uBAAuB;gBAC1B,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,wBAAwB;gBAC3B,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;gBACzC,MAAM;QACV,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,QAAgB,EAAE,IAAS;QACvD,MAAM,QAAQ,GAAiB;YAC7B,EAAE,EAAE,IAAI,CAAC,UAAU;YACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,QAAQ;YACR,UAAU,EAAE,MAAM;YAClB,MAAM,EAAE,SAAS;YACjB,QAAQ,EAAE,CAAC;YACX,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAEhD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,uBAAuB;YAC7B,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,IAAS;QACtC,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC3D,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC;YAC7B,iDAAiD;QACnD,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,MAAM,GAAG,UAAU,CAAC;YAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,QAAgB,EAAE,IAAiB;QACzD,wEAAwE;QACxE,mDAAmD;QACnD,OAAO,CAAC,GAAG,CACT,4BAA4B,QAAQ,WAAW,IAAI,CAAC,UAAU,QAAQ,CACvE,CAAC;IACJ,CAAC;IAED,wCAAwC;IAChC,KAAK,CAAC,iBAAiB,CAAC,IAAiB;QAC/C,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC/D,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;QACzD,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,kBAAkB;IAClB,cAAc,CACZ,UAAkB,EAClB,YAAwD;QAExD,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;QAE3B,2CAA2C;QAC3C,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,aAAa,GAAG;gBACpB,IAAI,EAAE,aAAa;gBACnB,UAAU;gBACV,MAAM,EAAE,gBAAgB;aACzB,CAAC;YAEF,MAAM,YAAY,GAChB,QAAQ,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACzE,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAExC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI,EAAE,wBAAwB;YAC9B,UAAU;YACV,QAAQ,EAAE,QAAQ,CAAC,QAAQ;SAC5B,CAAC,CAAC;IACL,CAAC;IAED,iBAAiB;IACjB,aAAa,CAAC,UAAkB;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,cAAc,EAAE,CAAC;YACnD,0DAA0D;YAC1D,OAAO,CAAC,GAAG,CAAC,YAAY,UAAU,SAAS,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,kBAAkB;IAClB,cAAc,CAAC,UAAkB;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,QAAQ,EAAE,CAAC;YACb,2DAA2D;YAC3D,OAAO,CAAC,GAAG,CAAC,YAAY,UAAU,UAAU,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;CACF"}