{"name": "@p2p/core", "version": "1.0.0", "description": "Core P2P functionality and connection management", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@p2p/types": "workspace:*", "socket.io-client": "^4.7.0"}, "devDependencies": {"@types/jest": "^29.5.0", "jest": "^29.5.0", "typescript": "^5.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}}