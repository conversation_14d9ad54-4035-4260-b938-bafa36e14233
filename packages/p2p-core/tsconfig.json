{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "ESNext", "moduleResolution": "node", "outDir": "./dist", "declaration": true, "declarationMap": true, "sourceMap": true, "noEmit": false, "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@p2p/types": ["../types/src"]}}, "include": ["src/**/*"], "exclude": ["dist", "node_modules", "**/*.test.ts"]}