{"name": "@p2p/ui", "version": "1.0.0", "description": "Reusable UI components for P2P application", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc && npm run build:fix-structure", "build:fix-structure": "cp -r dist/ui-components/src/* dist/ && rm -rf dist/ui-components dist/types", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix"}, "dependencies": {"@p2p/types": "workspace:*", "react": "^18.2.0", "react-dom": "^18.2.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}}