import type { Notification } from '@p2p/types';
export declare const useNotifications: () => {
    notifications: Notification[];
    addNotification: (type: Notification["type"], title: string, message: string, options?: {
        autoClose?: boolean;
        duration?: number;
    }) => string;
    removeNotification: (id: string) => void;
    clearAllNotifications: () => void;
    success: (title: string, message: string, options?: {
        autoClose?: boolean;
        duration?: number;
    }) => string;
    error: (title: string, message: string, options?: {
        autoClose?: boolean;
        duration?: number;
    }) => string;
    warning: (title: string, message: string, options?: {
        autoClose?: boolean;
        duration?: number;
    }) => string;
    info: (title: string, message: string, options?: {
        autoClose?: boolean;
        duration?: number;
    }) => string;
};
//# sourceMappingURL=useNotifications.d.ts.map