{"version": 3, "file": "useNotifications.js", "sourceRoot": "", "sources": ["useNotifications.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAG9C,MAAM,UAAU,GAAG,GAAW,EAAE;IAC9B,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;AAC3E,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,gBAAgB,GAAG,GAAG,EAAE;IACnC,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAiB,EAAE,CAAC,CAAC;IAEvE,MAAM,eAAe,GAAG,WAAW,CAAC,CAClC,IAA0B,EAC1B,KAAa,EACb,OAAe,EACf,OAGC,EACD,EAAE;QACF,MAAM,YAAY,GAAiB;YACjC,EAAE,EAAE,UAAU,EAAE;YAChB,IAAI;YACJ,KAAK;YACL,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,OAAO,EAAE,SAAS,IAAI,IAAI;YACrC,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,IAAI;SACpC,CAAC;QAEF,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;QAElD,mDAAmD;QACnD,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC;YAC3B,UAAU,CAAC,GAAG,EAAE;gBACd,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;YACtC,CAAC,EAAE,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,YAAY,CAAC,EAAE,CAAC;IACzB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,kBAAkB,GAAG,WAAW,CAAC,CAAC,EAAU,EAAE,EAAE;QACpD,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAChF,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,qBAAqB,GAAG,WAAW,CAAC,GAAG,EAAE;QAC7C,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,sBAAsB;IACtB,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,KAAa,EAAE,OAAe,EAAE,OAAoD,EAAE,EAAE;QACnH,OAAO,eAAe,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,MAAM,KAAK,GAAG,WAAW,CAAC,CAAC,KAAa,EAAE,OAAe,EAAE,OAAoD,EAAE,EAAE;QACjH,OAAO,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC3D,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,KAAa,EAAE,OAAe,EAAE,OAAoD,EAAE,EAAE;QACnH,OAAO,eAAe,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC7D,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,MAAM,IAAI,GAAG,WAAW,CAAC,CAAC,KAAa,EAAE,OAAe,EAAE,OAAoD,EAAE,EAAE;QAChH,OAAO,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IAC1D,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,OAAO;QACL,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,qBAAqB;QACrB,OAAO;QACP,KAAK;QACL,OAAO;QACP,IAAI;KACL,CAAC;AACJ,CAAC,CAAC"}