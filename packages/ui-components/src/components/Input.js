import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import clsx from 'clsx';
export const Input = ({ label, error, helperText, leftIcon, rightIcon, className, id, ...props }) => {
    const inputId = id || `input-${Math.random().toString(36).substring(2)}`;
    return (_jsxs("div", { className: "w-full", children: [label && (_jsx("label", { htmlFor: inputId, className: "block text-sm font-medium text-gray-700 mb-1", children: label })), _jsxs("div", { className: "relative", children: [leftIcon && (_jsx("div", { className: "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none", children: _jsx("div", { className: "h-5 w-5 text-gray-400", children: leftIcon }) })), _jsx("input", { id: inputId, className: clsx('block w-full px-3 py-2 border rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-0 sm:text-sm', leftIcon && 'pl-10', rightIcon && 'pr-10', error
                            ? 'border-error-300 focus:ring-error-500 focus:border-error-500'
                            : 'border-gray-300 focus:ring-primary-500 focus:border-primary-500', className), ...props }), rightIcon && (_jsx("div", { className: "absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none", children: _jsx("div", { className: "h-5 w-5 text-gray-400", children: rightIcon }) }))] }), error && (_jsx("p", { className: "mt-1 text-sm text-error-600", children: error })), helperText && !error && (_jsx("p", { className: "mt-1 text-sm text-gray-500", children: helperText }))] }));
};
//# sourceMappingURL=Input.js.map