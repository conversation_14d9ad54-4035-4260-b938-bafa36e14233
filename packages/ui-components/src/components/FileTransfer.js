import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useRef } from 'react';
import { Button } from './Button';
import { DocumentIcon, ArrowUpTrayIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';
export const FileTransferComponent = ({ transfer, onAccept, onReject, onCancel, onRetry, }) => {
    const formatFileSize = (bytes) => {
        if (bytes === 0)
            return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };
    const formatTransferSpeed = (bytesPerSecond) => {
        return formatFileSize(bytesPerSecond) + '/s';
    };
    const getStatusColor = (status) => {
        switch (status) {
            case 'pending':
                return 'text-yellow-600 bg-yellow-50';
            case 'accepted':
                return 'text-blue-600 bg-blue-50';
            case 'transferring':
                return 'text-blue-600 bg-blue-50';
            case 'completed':
                return 'text-green-600 bg-green-50';
            case 'failed':
                return 'text-red-600 bg-red-50';
            case 'rejected':
                return 'text-gray-600 bg-gray-50';
            default:
                return 'text-gray-600 bg-gray-50';
        }
    };
    const getStatusIcon = (status) => {
        switch (status) {
            case 'completed':
                return _jsx(CheckCircleIcon, { className: "h-5 w-5 text-green-500" });
            case 'failed':
                return _jsx(ExclamationTriangleIcon, { className: "h-5 w-5 text-red-500" });
            default:
                return _jsx(DocumentIcon, { className: "h-5 w-5 text-gray-400" });
        }
    };
    const isIncoming = transfer.receiverId === 'self';
    const isPending = transfer.status === 'pending';
    const isTransferring = transfer.status === 'transferring';
    const isCompleted = transfer.status === 'completed';
    const isFailed = transfer.status === 'failed';
    return (_jsx("div", { className: "bg-white border border-gray-200 rounded-lg p-4 shadow-sm", children: _jsxs("div", { className: "flex items-start space-x-3", children: [_jsx("div", { className: "flex-shrink-0", children: getStatusIcon(transfer.status) }), _jsxs("div", { className: "flex-1 min-w-0", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsx("p", { className: "text-sm font-medium text-gray-900 truncate", children: transfer.fileName }), _jsx("span", { className: clsx('inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium', getStatusColor(transfer.status)), children: transfer.status })] }), _jsxs("div", { className: "mt-1 flex items-center space-x-2 text-sm text-gray-500", children: [_jsx("span", { children: formatFileSize(transfer.fileSize) }), _jsx("span", { children: "\u2022" }), _jsxs("span", { children: [isIncoming ? 'from' : 'to', " ", isIncoming ? transfer.senderId : transfer.receiverId] })] }), (isTransferring || isCompleted) && (_jsxs("div", { className: "mt-3", children: [_jsxs("div", { className: "flex items-center justify-between text-sm", children: [_jsxs("span", { className: "text-gray-600", children: [Math.round(transfer.progress), "% complete"] }), transfer.startTime && transfer.endTime && (_jsxs("span", { className: "text-gray-500", children: [Math.round((transfer.endTime.getTime() - transfer.startTime.getTime()) / 1000), "s"] }))] }), _jsx("div", { className: "mt-1 w-full bg-gray-200 rounded-full h-2", children: _jsx("div", { className: clsx('h-2 rounded-full transition-all duration-300', isCompleted ? 'bg-green-500' : 'bg-blue-500'), style: { width: `${transfer.progress}%` } }) })] })), _jsxs("div", { className: "mt-3 flex items-center space-x-2", children: [isPending && isIncoming && (_jsxs(_Fragment, { children: [_jsx(Button, { size: "sm", onClick: () => onAccept?.(transfer.id), children: "Accept" }), _jsx(Button, { size: "sm", variant: "secondary", onClick: () => onReject?.(transfer.id), children: "Reject" })] })), isTransferring && (_jsx(Button, { size: "sm", variant: "danger", onClick: () => onCancel?.(transfer.id), children: "Cancel" })), isFailed && (_jsx(Button, { size: "sm", onClick: () => onRetry?.(transfer.id), children: "Retry" })), isCompleted && (_jsx(Button, { size: "sm", variant: "secondary", children: "Download" }))] })] })] }) }));
};
export const FileUpload = ({ onFileSelect, disabled = false, maxFileSize = 100 * 1024 * 1024, // 100MB default
acceptedTypes = [], }) => {
    const fileInputRef = useRef(null);
    const handleFileChange = (event) => {
        const file = event.target.files?.[0];
        if (!file)
            return;
        // Validate file size
        if (file.size > maxFileSize) {
            alert(`File size exceeds ${Math.round(maxFileSize / (1024 * 1024))}MB limit`);
            return;
        }
        // Validate file type
        if (acceptedTypes.length > 0 && !acceptedTypes.includes(file.type)) {
            alert('File type not supported');
            return;
        }
        onFileSelect(file);
        // Reset input
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };
    const handleClick = () => {
        fileInputRef.current?.click();
    };
    return (_jsxs("div", { children: [_jsx("input", { ref: fileInputRef, type: "file", onChange: handleFileChange, disabled: disabled, className: "hidden", accept: acceptedTypes.join(',') }), _jsxs(Button, { onClick: handleClick, disabled: disabled, variant: "secondary", size: "sm", children: [_jsx(ArrowUpTrayIcon, { className: "h-4 w-4 mr-2" }), "Send File"] })] }));
};
//# sourceMappingURL=FileTransfer.js.map