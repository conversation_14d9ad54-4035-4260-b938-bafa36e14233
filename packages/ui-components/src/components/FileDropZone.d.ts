import React from 'react';
interface FileDropZoneProps {
    onFileDrop: (files: File[]) => void;
    onFileSelect?: (files: File[]) => void;
    disabled?: boolean;
    maxFileSize?: number;
    maxFiles?: number;
    acceptedTypes?: string[];
    className?: string;
    children?: React.ReactNode;
}
export declare const FileDropZone: React.FC<FileDropZoneProps>;
interface FileListProps {
    files: File[];
    onRemove?: (index: number) => void;
    showRemove?: boolean;
}
export declare const FileList: React.FC<FileListProps>;
export {};
//# sourceMappingURL=FileDropZone.d.ts.map