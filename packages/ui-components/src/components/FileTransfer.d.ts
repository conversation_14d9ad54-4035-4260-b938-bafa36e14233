import React from 'react';
import type { FileTransfer } from '@p2p/types';
interface FileTransferProps {
    transfer: FileTransfer;
    onAccept?: (transferId: string) => void;
    onReject?: (transferId: string) => void;
    onCancel?: (transferId: string) => void;
    onRetry?: (transferId: string) => void;
}
export declare const FileTransferComponent: React.FC<FileTransferProps>;
interface FileUploadProps {
    onFileSelect: (file: File) => void;
    disabled?: boolean;
    maxFileSize?: number;
    acceptedTypes?: string[];
}
export declare const FileUpload: React.FC<FileUploadProps>;
export {};
//# sourceMappingURL=FileTransfer.d.ts.map