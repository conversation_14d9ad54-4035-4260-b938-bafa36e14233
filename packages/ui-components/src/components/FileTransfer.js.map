{"version": 3, "file": "FileTransfer.js", "sourceRoot": "", "sources": ["FileTransfer.tsx"], "names": [], "mappings": ";AAAA,OAAc,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AACtC,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAClC,OAAO,EACL,YAAY,EACZ,eAAe,EAEf,eAAe,EACf,uBAAuB,EACxB,MAAM,6BAA6B,CAAC;AAErC,OAAO,IAAI,MAAM,MAAM,CAAC;AAUxB,MAAM,CAAC,MAAM,qBAAqB,GAAgC,CAAC,EACjE,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,OAAO,GACR,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAClC,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAG,CAAC,cAAsB,EAAU,EAAE;QAC7D,OAAO,cAAc,CAAC,cAAc,CAAC,GAAG,IAAI,CAAC;IAC/C,CAAC,CAAC;IAEF,MAAM,cAAc,GAAG,CAAC,MAA8B,EAAE,EAAE;QACxD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,SAAS;gBACZ,OAAO,8BAA8B,CAAC;YACxC,KAAK,UAAU;gBACb,OAAO,0BAA0B,CAAC;YACpC,KAAK,cAAc;gBACjB,OAAO,0BAA0B,CAAC;YACpC,KAAK,WAAW;gBACd,OAAO,4BAA4B,CAAC;YACtC,KAAK,QAAQ;gBACX,OAAO,wBAAwB,CAAC;YAClC,KAAK,UAAU;gBACb,OAAO,0BAA0B,CAAC;YACpC;gBACE,OAAO,0BAA0B,CAAC;QACtC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,MAA8B,EAAE,EAAE;QACvD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,WAAW;gBACd,OAAO,KAAC,eAAe,IAAC,SAAS,EAAC,wBAAwB,GAAG,CAAC;YAChE,KAAK,QAAQ;gBACX,OAAO,KAAC,uBAAuB,IAAC,SAAS,EAAC,sBAAsB,GAAG,CAAC;YACtE;gBACE,OAAO,KAAC,YAAY,IAAC,SAAS,EAAC,uBAAuB,GAAG,CAAC;QAC9D,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,KAAK,MAAM,CAAC;IAClD,MAAM,SAAS,GAAG,QAAQ,CAAC,MAAM,KAAK,SAAS,CAAC;IAChD,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,KAAK,cAAc,CAAC;IAC1D,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,KAAK,WAAW,CAAC;IACpD,MAAM,QAAQ,GAAG,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC;IAE9C,OAAO,CACL,cAAK,SAAS,EAAC,0DAA0D,YACvE,eAAK,SAAS,EAAC,4BAA4B,aAEzC,cAAK,SAAS,EAAC,eAAe,YAC3B,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,GAC3B,EAGN,eAAK,SAAS,EAAC,gBAAgB,aAC7B,eAAK,SAAS,EAAC,mCAAmC,aAChD,YAAG,SAAS,EAAC,4CAA4C,YACtD,QAAQ,CAAC,QAAQ,GAChB,EACJ,eACE,SAAS,EAAE,IAAI,CACb,yEAAyE,EACzE,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAChC,YAEA,QAAQ,CAAC,MAAM,GACX,IACH,EAEN,eAAK,SAAS,EAAC,wDAAwD,aACrE,yBAAO,cAAc,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAQ,EAChD,oCAAc,EACd,2BAAO,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,OAAG,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,UAAU,IAAQ,IAC5F,EAGL,CAAC,cAAc,IAAI,WAAW,CAAC,IAAI,CAClC,eAAK,SAAS,EAAC,MAAM,aACnB,eAAK,SAAS,EAAC,2CAA2C,aACxD,gBAAM,SAAS,EAAC,eAAe,aAC5B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,kBACzB,EACN,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,OAAO,IAAI,CACzC,gBAAM,SAAS,EAAC,eAAe,aAC5B,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI,CAAC,SAC1E,CACR,IACG,EACN,cAAK,SAAS,EAAC,0CAA0C,YACvD,cACE,SAAS,EAAE,IAAI,CACb,8CAA8C,EAC9C,WAAW,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,aAAa,CAC7C,EACD,KAAK,EAAE,EAAE,KAAK,EAAE,GAAG,QAAQ,CAAC,QAAQ,GAAG,EAAE,GACzC,GACE,IACF,CACP,EAGD,eAAK,SAAS,EAAC,kCAAkC,aAC9C,SAAS,IAAI,UAAU,IAAI,CAC1B,8BACE,KAAC,MAAM,IACL,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,uBAG/B,EACT,KAAC,MAAM,IACL,IAAI,EAAC,IAAI,EACT,OAAO,EAAC,WAAW,EACnB,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,uBAG/B,IACR,CACJ,EAEA,cAAc,IAAI,CACjB,KAAC,MAAM,IACL,IAAI,EAAC,IAAI,EACT,OAAO,EAAC,QAAQ,EAChB,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,uBAG/B,CACV,EAEA,QAAQ,IAAI,CACX,KAAC,MAAM,IACL,IAAI,EAAC,IAAI,EACT,OAAO,EAAE,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,sBAG9B,CACV,EAEA,WAAW,IAAI,CACd,KAAC,MAAM,IACL,IAAI,EAAC,IAAI,EACT,OAAO,EAAC,WAAW,yBAGZ,CACV,IACG,IACF,IACF,GACF,CACP,CAAC;AACJ,CAAC,CAAC;AASF,MAAM,CAAC,MAAM,UAAU,GAA8B,CAAC,EACpD,YAAY,EACZ,QAAQ,GAAG,KAAK,EAChB,WAAW,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,gBAAgB;AACjD,aAAa,GAAG,EAAE,GACnB,EAAE,EAAE;IACH,MAAM,YAAY,GAAG,MAAM,CAAmB,IAAI,CAAC,CAAC;IAEpD,MAAM,gBAAgB,GAAG,CAAC,KAA0C,EAAE,EAAE;QACtE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,qBAAqB;QACrB,IAAI,IAAI,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC;YAC5B,KAAK,CAAC,qBAAqB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,UAAU,CAAC,CAAC;YAC9E,OAAO;QACT,CAAC;QAED,qBAAqB;QACrB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YACnE,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACjC,OAAO;QACT,CAAC;QAED,YAAY,CAAC,IAAI,CAAC,CAAC;QAEnB,cAAc;QACd,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;YACzB,YAAY,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;QAClC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;IAChC,CAAC,CAAC;IAEF,OAAO,CACL,0BACE,gBACE,GAAG,EAAE,YAAY,EACjB,IAAI,EAAC,MAAM,EACX,QAAQ,EAAE,gBAAgB,EAC1B,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAC,QAAQ,EAClB,MAAM,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAC/B,EACF,MAAC,MAAM,IACL,OAAO,EAAE,WAAW,EACpB,QAAQ,EAAE,QAAQ,EAClB,OAAO,EAAC,WAAW,EACnB,IAAI,EAAC,IAAI,aAET,KAAC,eAAe,IAAC,SAAS,EAAC,cAAc,GAAG,iBAErC,IACL,CACP,CAAC;AACJ,CAAC,CAAC"}