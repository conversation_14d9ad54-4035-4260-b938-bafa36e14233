{"version": 3, "file": "FileDropZone.js", "sourceRoot": "", "sources": ["FileDropZone.tsx"], "names": [], "mappings": ";AAAA,OAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AAC7D,OAAO,IAAI,MAAM,MAAM,CAAC;AACxB,OAAO,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM,6BAA6B,CAAC;AAa7E,MAAM,CAAC,MAAM,YAAY,GAAgC,CAAC,EACxD,UAAU,EACV,YAAY,EACZ,QAAQ,GAAG,KAAK,EAChB,WAAW,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI,EAAE,gBAAgB;AACjD,QAAQ,GAAG,EAAE,EACb,aAAa,GAAG,EAAE,EAClB,SAAS,EACT,QAAQ,GACT,EAAE,EAAE;IACH,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAClD,MAAM,YAAY,GAAG,MAAM,CAAmB,IAAI,CAAC,CAAC;IAEpD,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAClC,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAAa,EAAU,EAAE;QAC9C,MAAM,UAAU,GAAW,EAAE,CAAC;QAC9B,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,kBAAkB;YAClB,IAAI,IAAI,CAAC,IAAI,GAAG,WAAW,EAAE,CAAC;gBAC5B,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,uBAAuB,cAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;gBACpF,SAAS;YACX,CAAC;YAED,kBAAkB;YAClB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBACnE,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,2BAA2B,CAAC,CAAC;gBACrD,SAAS;YACX,CAAC;YAED,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;QAED,kBAAkB;QAClB,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,EAAE,CAAC;YACjC,MAAM,CAAC,IAAI,CAAC,2BAA2B,QAAQ,iBAAiB,CAAC,CAAC;YAClE,OAAO,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;QACvC,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC3B,CAAC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC,CAAC;IAEF,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,CAAkB,EAAE,EAAE;QACzD,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QAEpB,IAAI,QAAQ;YAAE,OAAO;QAErB,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;QAEjC,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5D,aAAa,CAAC,IAAI,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,CAAkB,EAAE,EAAE;QACzD,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QAEpB,IAAI,QAAQ;YAAE,OAAO;QAErB,cAAc,CAAC,IAAI,CAAC,EAAE;YACpB,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC;YAC5B,IAAI,UAAU,KAAK,CAAC,EAAE,CAAC;gBACrB,aAAa,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;YACD,OAAO,UAAU,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,CAAkB,EAAE,EAAE;QACxD,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;IACtB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAkB,EAAE,EAAE;QACpD,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,eAAe,EAAE,CAAC;QAEpB,IAAI,QAAQ;YAAE,OAAO;QAErB,aAAa,CAAC,KAAK,CAAC,CAAC;QACrB,cAAc,CAAC,CAAC,CAAC,CAAC;QAElB,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC/C,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QAExC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,UAAU,CAAC,UAAU,CAAC,CAAC;QACzB,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC,CAAC,CAAC;IAE1C,MAAM,qBAAqB,GAAG,CAAC,CAAsC,EAAE,EAAE;QACvE,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;QAC/C,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;QAExC,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1B,YAAY,EAAE,CAAC,UAAU,CAAC,CAAC;QAC7B,CAAC;QAED,cAAc;QACd,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;YACzB,YAAY,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;QAClC,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,YAAY,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;QAChC,CAAC;IACH,CAAC,CAAC;IAEF,OAAO,CACL,eACE,SAAS,EAAE,IAAI,CACb,2EAA2E,EAC3E,UAAU,IAAI,CAAC,QAAQ;YACrB,CAAC,CAAC,kCAAkC;YACpC,CAAC,CAAC,uCAAuC,EAC3C,QAAQ,IAAI,+BAA+B,EAC3C,CAAC,QAAQ,IAAI,gBAAgB,EAC7B,SAAS,CACV,EACD,WAAW,EAAE,eAAe,EAC5B,WAAW,EAAE,eAAe,EAC5B,UAAU,EAAE,cAAc,EAC1B,MAAM,EAAE,UAAU,EAClB,OAAO,EAAE,WAAW,aAEpB,gBACE,GAAG,EAAE,YAAY,EACjB,IAAI,EAAC,MAAM,EACX,QAAQ,EAAE,QAAQ,GAAG,CAAC,EACtB,QAAQ,EAAE,qBAAqB,EAC/B,QAAQ,EAAE,QAAQ,EAClB,SAAS,EAAC,QAAQ,EAClB,MAAM,EAAE,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAC/B,EAED,QAAQ,IAAI,CACX,eAAK,SAAS,EAAC,kEAAkE,aAC/E,KAAC,gBAAgB,IACf,SAAS,EAAE,IAAI,CACb,gBAAgB,EAChB,UAAU,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,eAAe,CAClD,GACD,EACF,YAAG,SAAS,EAAC,wCAAwC,YAClD,UAAU,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,qBAAqB,GACrD,EACJ,YAAG,SAAS,EAAC,4BAA4B,yCAErC,EACJ,eAAK,SAAS,EAAC,iCAAiC,aAC7C,WAAW,IAAI,CACd,+CAAuB,cAAc,CAAC,WAAW,CAAC,IAAK,CACxD,EACA,QAAQ,GAAG,CAAC,IAAI,CACf,oCAAY,QAAQ,cAAW,CAChC,EACA,aAAa,CAAC,MAAM,GAAG,CAAC,IAAI,CAC3B,4CAAoB,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAK,CAClD,IACG,IACF,CACP,EAEA,UAAU,IAAI,CACb,cAAK,SAAS,EAAC,8EAA8E,GAAG,CACjG,IACG,CACP,CAAC;AACJ,CAAC,CAAC;AAQF,MAAM,CAAC,MAAM,QAAQ,GAA4B,CAAC,EAChD,KAAK,EACL,QAAQ,EACR,UAAU,GAAG,IAAI,GAClB,EAAE,EAAE;IACH,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE;QAC/C,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAClC,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC,CAAC;IAEF,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,cAAK,SAAS,EAAC,WAAW,YACvB,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC,CAC1B,eAEE,SAAS,EAAC,6DAA6D,aAEvE,eAAK,SAAS,EAAC,6BAA6B,aAC1C,KAAC,YAAY,IAAC,SAAS,EAAC,uBAAuB,GAAG,EAClD,0BACE,YAAG,SAAS,EAAC,mCAAmC,YAAE,IAAI,CAAC,IAAI,GAAK,EAChE,YAAG,SAAS,EAAC,uBAAuB,YAAE,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAK,IAChE,IACF,EACL,UAAU,IAAI,QAAQ,IAAI,CACzB,kBACE,OAAO,EAAE,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,EAC9B,SAAS,EAAC,oDAAoD,aAE9D,eAAM,SAAS,EAAC,SAAS,4BAAmB,cAErC,CACV,KAlBI,GAAG,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,CAmBxB,CACP,CAAC,GACE,CACP,CAAC;AACJ,CAAC,CAAC"}