import { jsx as _jsx } from "react/jsx-runtime";
import clsx from 'clsx';
export const Card = ({ children, className, padding = 'md', shadow = 'sm', }) => {
    const paddingClasses = {
        none: '',
        sm: 'p-3',
        md: 'p-6',
        lg: 'p-8',
    };
    const shadowClasses = {
        none: '',
        sm: 'shadow-sm',
        md: 'shadow-md',
        lg: 'shadow-lg',
    };
    return (_jsx("div", { className: clsx('bg-white rounded-lg border border-gray-200', paddingClasses[padding], shadowClasses[shadow], className), children: children }));
};
//# sourceMappingURL=Card.js.map