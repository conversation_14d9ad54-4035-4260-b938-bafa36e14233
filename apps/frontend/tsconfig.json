{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "rootDir": "./src", "paths": {"@/*": ["./src/*"], "@p2p/core": ["../../packages/p2p-core/src"], "@p2p/types": ["../../packages/types/src"], "@p2p/ui": ["../../packages/ui-components/src"]}}, "include": ["src/**/*"], "references": [{"path": "./tsconfig.node.json"}]}