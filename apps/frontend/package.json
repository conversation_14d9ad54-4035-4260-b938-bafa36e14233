{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --project tsconfig.build.json && vite build", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "clsx": "^2.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "socket.io-client": "^4.7.0", "zustand": "^4.4.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.0.0", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.0", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "jsdom": "^26.1.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0", "vite": "^4.4.0", "vitest": "^3.2.4"}}