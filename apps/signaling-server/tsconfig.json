{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@p2p/types": ["../../packages/types/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "src/test/**/*", "**/*.test.ts"]}