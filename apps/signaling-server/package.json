{"name": "signaling-server", "version": "1.0.0", "description": "Minimal signaling server for P2P connections", "private": true, "type": "module", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext .ts --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^4.18.0", "helmet": "^7.0.0", "socket.io": "^4.7.0"}, "devDependencies": {"@types/compression": "^1.7.0", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^8.57.1", "socket.io-client": "^4.7.0", "supertest": "^7.1.3", "tsx": "^3.12.0", "typescript": "^5.0.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}