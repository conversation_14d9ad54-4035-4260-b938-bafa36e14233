{"/Users/<USER>/dev/workspace/apps/signaling-server/src/index.ts": {"path": "/Users/<USER>/dev/workspace/apps/signaling-server/src/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 36}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 35}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 24}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 28}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 38}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 28}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 66}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 16}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 22}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 33}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 31}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 9}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 64}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 29}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 22}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 4}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 39}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 3}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 8}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 10}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 60}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 4}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 2}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 23}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 8}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 8}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 64}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 22}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 4}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 2}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 24}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 35}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 12}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 17}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 40}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 29}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 56}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 5}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 3}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 38}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 44}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 18}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 3}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 25}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 38}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 27}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 61}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 63}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 14}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 77}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 4}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 3}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 29}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 60}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 22}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 33}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 20}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 5}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 3}}, "78": {"start": {"line": 79, "column": 0}, "end": {"line": 79, "column": 28}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 59}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 22}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 33}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 20}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 5}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 3}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "10": 0, "12": 0, "13": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "51": 0, "52": 0, "53": 0, "54": 0, "57": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "78": 0, "79": 0, "80": 0, "81": 0, "82": 0, "83": 0, "84": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -624}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -624}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -624}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 85, "column": -624}}, "line": 1}}, "f": {"0": 0}}, "/Users/<USER>/dev/workspace/apps/signaling-server/src/services/SignalingService.ts": {"path": "/Users/<USER>/dev/workspace/apps/signaling-server/src/services/SignalingService.ts", "all": false, "statementMap": {"26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 31}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 56}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 53}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 26}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 24}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 25}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 18}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 23}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 4}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 35}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 31}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 3}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 33}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 50}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 55}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 48}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 49}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 50}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 44}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 9}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 16}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 16}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 73}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 51}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 9}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 8}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 16}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 17}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 74}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 52}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 9}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 8}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 16}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 24}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 71}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 59}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 9}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 8}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 37}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 38}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 9}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 37}}, "80": {"start": {"line": 81, "column": 0}, "end": {"line": 81, "column": 37}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 9}}, "82": {"start": {"line": 83, "column": 0}, "end": {"line": 83, "column": 7}}, "83": {"start": {"line": 84, "column": 0}, "end": {"line": 84, "column": 3}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 58}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 59}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 33}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 24}}, "95": {"start": {"line": 96, "column": 0}, "end": {"line": 96, "column": 46}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 52}}, "97": {"start": {"line": 98, "column": 0}, "end": {"line": 98, "column": 42}}, "98": {"start": {"line": 99, "column": 0}, "end": {"line": 99, "column": 5}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 53}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 43}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 65}}, "107": {"start": {"line": 108, "column": 0}, "end": {"line": 108, "column": 65}}, "110": {"start": {"line": 111, "column": 0}, "end": {"line": 111, "column": 24}}, "111": {"start": {"line": 112, "column": 0}, "end": {"line": 112, "column": 43}}, "112": {"start": {"line": 113, "column": 0}, "end": {"line": 113, "column": 20}}, "113": {"start": {"line": 114, "column": 0}, "end": {"line": 114, "column": 13}}, "114": {"start": {"line": 115, "column": 0}, "end": {"line": 115, "column": 27}}, "115": {"start": {"line": 116, "column": 0}, "end": {"line": 116, "column": 7}}, "117": {"start": {"line": 118, "column": 0}, "end": {"line": 118, "column": 16}}, "118": {"start": {"line": 119, "column": 0}, "end": {"line": 119, "column": 69}}, "119": {"start": {"line": 120, "column": 0}, "end": {"line": 120, "column": 6}}, "120": {"start": {"line": 121, "column": 0}, "end": {"line": 121, "column": 3}}, "122": {"start": {"line": 123, "column": 0}, "end": {"line": 123, "column": 43}}, "123": {"start": {"line": 124, "column": 0}, "end": {"line": 124, "column": 59}}, "124": {"start": {"line": 125, "column": 0}, "end": {"line": 125, "column": 26}}, "126": {"start": {"line": 127, "column": 0}, "end": {"line": 127, "column": 32}}, "127": {"start": {"line": 128, "column": 0}, "end": {"line": 128, "column": 52}}, "129": {"start": {"line": 130, "column": 0}, "end": {"line": 130, "column": 15}}, "130": {"start": {"line": 131, "column": 0}, "end": {"line": 131, "column": 29}}, "131": {"start": {"line": 132, "column": 0}, "end": {"line": 132, "column": 27}}, "134": {"start": {"line": 135, "column": 0}, "end": {"line": 135, "column": 65}}, "137": {"start": {"line": 138, "column": 0}, "end": {"line": 138, "column": 28}}, "138": {"start": {"line": 139, "column": 0}, "end": {"line": 139, "column": 46}}, "139": {"start": {"line": 140, "column": 0}, "end": {"line": 140, "column": 58}}, "140": {"start": {"line": 141, "column": 0}, "end": {"line": 141, "column": 7}}, "142": {"start": {"line": 143, "column": 0}, "end": {"line": 143, "column": 18}}, "143": {"start": {"line": 144, "column": 0}, "end": {"line": 144, "column": 70}}, "144": {"start": {"line": 145, "column": 0}, "end": {"line": 145, "column": 8}}, "145": {"start": {"line": 146, "column": 0}, "end": {"line": 146, "column": 5}}, "147": {"start": {"line": 148, "column": 0}, "end": {"line": 148, "column": 45}}, "148": {"start": {"line": 149, "column": 0}, "end": {"line": 149, "column": 3}}, "150": {"start": {"line": 151, "column": 0}, "end": {"line": 151, "column": 44}}, "151": {"start": {"line": 152, "column": 0}, "end": {"line": 152, "column": 56}}, "152": {"start": {"line": 153, "column": 0}, "end": {"line": 153, "column": 47}}, "154": {"start": {"line": 155, "column": 0}, "end": {"line": 155, "column": 33}}, "155": {"start": {"line": 156, "column": 0}, "end": {"line": 156, "column": 3}}, "157": {"start": {"line": 158, "column": 0}, "end": {"line": 158, "column": 23}}, "158": {"start": {"line": 159, "column": 0}, "end": {"line": 159, "column": 19}}, "159": {"start": {"line": 160, "column": 0}, "end": {"line": 160, "column": 18}}, "160": {"start": {"line": 161, "column": 0}, "end": {"line": 161, "column": 48}}, "161": {"start": {"line": 162, "column": 0}, "end": {"line": 162, "column": 5}}, "162": {"start": {"line": 163, "column": 0}, "end": {"line": 163, "column": 40}}, "165": {"start": {"line": 166, "column": 0}, "end": {"line": 166, "column": 31}}, "166": {"start": {"line": 167, "column": 0}, "end": {"line": 167, "column": 17}}, "167": {"start": {"line": 168, "column": 0}, "end": {"line": 168, "column": 24}}, "168": {"start": {"line": 169, "column": 0}, "end": {"line": 169, "column": 6}}, "171": {"start": {"line": 172, "column": 0}, "end": {"line": 172, "column": 53}}, "172": {"start": {"line": 173, "column": 0}, "end": {"line": 173, "column": 45}}, "174": {"start": {"line": 175, "column": 0}, "end": {"line": 175, "column": 70}}, "175": {"start": {"line": 176, "column": 0}, "end": {"line": 176, "column": 3}}, "177": {"start": {"line": 178, "column": 0}, "end": {"line": 178, "column": 28}}, "178": {"start": {"line": 179, "column": 0}, "end": {"line": 179, "column": 12}}, "179": {"start": {"line": 180, "column": 0}, "end": {"line": 180, "column": 32}}, "180": {"start": {"line": 181, "column": 0}, "end": {"line": 181, "column": 62}}, "181": {"start": {"line": 182, "column": 0}, "end": {"line": 182, "column": 27}}, "182": {"start": {"line": 183, "column": 0}, "end": {"line": 183, "column": 13}}, "183": {"start": {"line": 184, "column": 0}, "end": {"line": 184, "column": 32}}, "184": {"start": {"line": 185, "column": 0}, "end": {"line": 185, "column": 35}}, "185": {"start": {"line": 186, "column": 0}, "end": {"line": 186, "column": 10}}, "186": {"start": {"line": 187, "column": 0}, "end": {"line": 187, "column": 8}}, "187": {"start": {"line": 188, "column": 0}, "end": {"line": 188, "column": 42}}, "188": {"start": {"line": 189, "column": 0}, "end": {"line": 189, "column": 6}}, "189": {"start": {"line": 190, "column": 0}, "end": {"line": 190, "column": 3}}, "191": {"start": {"line": 192, "column": 0}, "end": {"line": 192, "column": 45}}, "192": {"start": {"line": 193, "column": 0}, "end": {"line": 193, "column": 79}}, "193": {"start": {"line": 194, "column": 0}, "end": {"line": 194, "column": 9}}, "194": {"start": {"line": 195, "column": 0}, "end": {"line": 195, "column": 28}}, "195": {"start": {"line": 196, "column": 0}, "end": {"line": 196, "column": 87}}, "196": {"start": {"line": 197, "column": 0}, "end": {"line": 197, "column": 8}}, "197": {"start": {"line": 198, "column": 0}, "end": {"line": 198, "column": 3}}, "198": {"start": {"line": 199, "column": 0}, "end": {"line": 199, "column": 1}}}, "s": {"26": 1, "27": 1, "28": 1, "29": 1, "30": 5, "31": 5, "32": 5, "33": 5, "34": 5, "36": 1, "37": 67, "38": 67, "40": 1, "41": 67, "42": 40, "43": 40, "44": 40, "47": 40, "48": 27, "49": 40, "52": 40, "53": 40, "54": 40, "55": 3, "56": 3, "57": 40, "59": 40, "60": 40, "61": 40, "62": 2, "63": 2, "64": 40, "66": 40, "67": 40, "68": 40, "69": 3, "70": 3, "71": 40, "74": 40, "75": 26, "76": 40, "79": 40, "80": 5, "81": 40, "82": 67, "83": 67, "85": 1, "86": 27, "89": 27, "92": 27, "95": 27, "96": 19, "97": 19, "98": 19, "100": 27, "103": 27, "104": 27, "107": 27, "110": 27, "111": 27, "112": 27, "113": 27, "114": 27, "115": 27, "117": 27, "118": 27, "119": 27, "120": 27, "122": 1, "123": 58, "124": 58, "126": 20, "127": 20, "129": 20, "130": 20, "131": 20, "134": 20, "137": 20, "138": 13, "139": 13, "140": 13, "142": 20, "143": 20, "144": 20, "145": 20, "147": 20, "148": 58, "150": 1, "151": 26, "152": 26, "154": 26, "155": 26, "157": 1, "158": 8, "159": 8, "160": 8, "161": 8, "162": 8, "165": 8, "166": 8, "167": 8, "168": 8, "171": 8, "172": 8, "174": 8, "175": 8, "177": 1, "178": 32, "179": 32, "180": 32, "181": 32, "182": 14, "183": 14, "184": 14, "185": 14, "186": 32, "187": 32, "188": 32, "189": 32, "191": 1, "192": 2, "193": 1, "194": 1, "195": 1, "196": 2, "197": 2, "198": 1}, "branchMap": {"0": {"type": "branch", "line": 37, "loc": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "locations": [{"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}]}, "1": {"type": "branch", "line": 30, "loc": {"start": {"line": 30, "column": 2}, "end": {"line": 35, "column": 4}}, "locations": [{"start": {"line": 30, "column": 2}, "end": {"line": 35, "column": 4}}]}, "2": {"type": "branch", "line": 41, "loc": {"start": {"line": 41, "column": 10}, "end": {"line": 84, "column": 3}}, "locations": [{"start": {"line": 41, "column": 10}, "end": {"line": 84, "column": 3}}]}, "3": {"type": "branch", "line": 42, "loc": {"start": {"line": 42, "column": 29}, "end": {"line": 83, "column": 5}}, "locations": [{"start": {"line": 42, "column": 29}, "end": {"line": 83, "column": 5}}]}, "4": {"type": "branch", "line": 48, "loc": {"start": {"line": 48, "column": 29}, "end": {"line": 50, "column": 7}}, "locations": [{"start": {"line": 48, "column": 29}, "end": {"line": 50, "column": 7}}]}, "5": {"type": "branch", "line": 55, "loc": {"start": {"line": 55, "column": 8}, "end": {"line": 57, "column": 9}}, "locations": [{"start": {"line": 55, "column": 8}, "end": {"line": 57, "column": 9}}]}, "6": {"type": "branch", "line": 62, "loc": {"start": {"line": 62, "column": 8}, "end": {"line": 64, "column": 9}}, "locations": [{"start": {"line": 62, "column": 8}, "end": {"line": 64, "column": 9}}]}, "7": {"type": "branch", "line": 69, "loc": {"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": 9}}, "locations": [{"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": 9}}]}, "8": {"type": "branch", "line": 75, "loc": {"start": {"line": 75, "column": 30}, "end": {"line": 77, "column": 7}}, "locations": [{"start": {"line": 75, "column": 30}, "end": {"line": 77, "column": 7}}]}, "9": {"type": "branch", "line": 80, "loc": {"start": {"line": 80, "column": 30}, "end": {"line": 82, "column": 7}}, "locations": [{"start": {"line": 80, "column": 30}, "end": {"line": 82, "column": 7}}]}, "10": {"type": "branch", "line": 86, "loc": {"start": {"line": 86, "column": 10}, "end": {"line": 121, "column": 3}}, "locations": [{"start": {"line": 86, "column": 10}, "end": {"line": 121, "column": 3}}]}, "11": {"type": "branch", "line": 96, "loc": {"start": {"line": 96, "column": 45}, "end": {"line": 99, "column": 5}}, "locations": [{"start": {"line": 96, "column": 45}, "end": {"line": 99, "column": 5}}]}, "12": {"type": "branch", "line": 123, "loc": {"start": {"line": 123, "column": 10}, "end": {"line": 149, "column": 3}}, "locations": [{"start": {"line": 123, "column": 10}, "end": {"line": 149, "column": 3}}]}, "13": {"type": "branch", "line": 125, "loc": {"start": {"line": 125, "column": 19}, "end": {"line": 125, "column": 26}}, "locations": [{"start": {"line": 125, "column": 19}, "end": {"line": 125, "column": 26}}]}, "14": {"type": "branch", "line": 125, "loc": {"start": {"line": 125, "column": 19}, "end": {"line": 148, "column": 45}}, "locations": [{"start": {"line": 125, "column": 19}, "end": {"line": 148, "column": 45}}]}, "15": {"type": "branch", "line": 138, "loc": {"start": {"line": 138, "column": 27}, "end": {"line": 141, "column": 7}}, "locations": [{"start": {"line": 138, "column": 27}, "end": {"line": 141, "column": 7}}]}, "16": {"type": "branch", "line": 151, "loc": {"start": {"line": 151, "column": 10}, "end": {"line": 156, "column": 3}}, "locations": [{"start": {"line": 151, "column": 10}, "end": {"line": 156, "column": 3}}]}, "17": {"type": "branch", "line": 158, "loc": {"start": {"line": 158, "column": 10}, "end": {"line": 176, "column": 3}}, "locations": [{"start": {"line": 158, "column": 10}, "end": {"line": 176, "column": 3}}]}, "18": {"type": "branch", "line": 178, "loc": {"start": {"line": 178, "column": 16}, "end": {"line": 190, "column": 3}}, "locations": [{"start": {"line": 178, "column": 16}, "end": {"line": 190, "column": 3}}]}, "19": {"type": "branch", "line": 182, "loc": {"start": {"line": 182, "column": 8}, "end": {"line": 186, "column": 10}}, "locations": [{"start": {"line": 182, "column": 8}, "end": {"line": 186, "column": 10}}]}, "20": {"type": "branch", "line": 192, "loc": {"start": {"line": 192, "column": 16}, "end": {"line": 198, "column": 3}}, "locations": [{"start": {"line": 192, "column": 16}, "end": {"line": 198, "column": 3}}]}, "21": {"type": "branch", "line": 193, "loc": {"start": {"line": 193, "column": 60}, "end": {"line": 197, "column": 6}}, "locations": [{"start": {"line": 193, "column": 60}, "end": {"line": 197, "column": 6}}]}}, "b": {"0": [67], "1": [5], "2": [67], "3": [40], "4": [27], "5": [3], "6": [2], "7": [3], "8": [26], "9": [5], "10": [27], "11": [19], "12": [58], "13": [38], "14": [20], "15": [13], "16": [26], "17": [8], "18": [32], "19": [14], "20": [2], "21": [1]}, "fnMap": {"0": {"name": "SignalingService", "decl": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "loc": {"start": {"line": 37, "column": 2}, "end": {"line": 39, "column": 3}}, "line": 37}, "1": {"name": "<static_initializer>", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 35, "column": 4}}, "loc": {"start": {"line": 30, "column": 2}, "end": {"line": 35, "column": 4}}, "line": 30}, "2": {"name": "setupSocketHandlers", "decl": {"start": {"line": 41, "column": 10}, "end": {"line": 84, "column": 3}}, "loc": {"start": {"line": 41, "column": 10}, "end": {"line": 84, "column": 3}}, "line": 41}, "3": {"name": "handleJoinRoom", "decl": {"start": {"line": 86, "column": 10}, "end": {"line": 121, "column": 3}}, "loc": {"start": {"line": 86, "column": 10}, "end": {"line": 121, "column": 3}}, "line": 86}, "4": {"name": "handleLeaveRoom", "decl": {"start": {"line": 123, "column": 10}, "end": {"line": 149, "column": 3}}, "loc": {"start": {"line": 123, "column": 10}, "end": {"line": 149, "column": 3}}, "line": 123}, "5": {"name": "handleDisconnect", "decl": {"start": {"line": 151, "column": 10}, "end": {"line": 156, "column": 3}}, "loc": {"start": {"line": 151, "column": 10}, "end": {"line": 156, "column": 3}}, "line": 151}, "6": {"name": "relayMessage", "decl": {"start": {"line": 158, "column": 10}, "end": {"line": 176, "column": 3}}, "loc": {"start": {"line": 158, "column": 10}, "end": {"line": 176, "column": 3}}, "line": 158}, "7": {"name": "getStats", "decl": {"start": {"line": 178, "column": 16}, "end": {"line": 190, "column": 3}}, "loc": {"start": {"line": 178, "column": 16}, "end": {"line": 190, "column": 3}}, "line": 178}, "8": {"name": "getRoomStats", "decl": {"start": {"line": 192, "column": 16}, "end": {"line": 198, "column": 3}}, "loc": {"start": {"line": 192, "column": 16}, "end": {"line": 198, "column": 3}}, "line": 192}}, "f": {"0": 67, "1": 5, "2": 67, "3": 27, "4": 58, "5": 26, "6": 8, "7": 32, "8": 2}}}