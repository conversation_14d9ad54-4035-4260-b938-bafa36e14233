{"name": "p2p-app", "version": "1.0.0", "description": "A peer-to-peer application with real-time chat and file transfer capabilities", "private": true, "type": "module", "scripts": {"build": "pnpm build:packages && pnpm build:apps", "build:packages": "pnpm --filter './packages/*' build", "build:apps": "pnpm --filter './apps/*' build", "dev": "pnpm build:packages && pnpm --parallel --filter './apps/*' dev", "dev:packages": "pnpm --filter './packages/*' dev", "dev:apps": "pnpm --filter './apps/*' dev", "dev:frontend": "pnpm --filter frontend dev", "dev:server": "pnpm --filter signaling-server dev", "build:frontend": "pnpm --filter frontend build", "build:server": "pnpm --filter signaling-server build", "start:server": "pnpm --filter signaling-server start", "test": "pnpm --recursive test", "test:packages": "pnpm --filter './packages/*' test", "test:apps": "pnpm --filter './apps/*' test", "lint": "pnpm --recursive lint", "lint:fix": "pnpm --recursive lint:fix", "type-check": "pnpm --recursive type-check", "clean": "pnpm --recursive clean", "test:server": "pnpm --filter signaling-server test:run", "test:server:coverage": "pnpm --filter signaling-server test:coverage", "test:server:watch": "pnpm --filter signaling-server test", "test:frontend": "pnpm --filter frontend test:run", "test:frontend:coverage": "pnpm --filter frontend test:coverage", "test:frontend:watch": "pnpm --filter frontend test", "test:all": "pnpm test:server && pnpm test:frontend", "test:all:coverage": "pnpm test:server:coverage && pnpm test:frontend:coverage"}, "keywords": ["p2p", "webrtc", "chat", "file-transfer", "react", "typescript"], "author": "P2P App Team", "license": "MIT", "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}